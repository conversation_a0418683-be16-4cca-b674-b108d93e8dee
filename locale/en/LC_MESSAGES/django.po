# English translation for Django
# Copyright (C) 2024
# This file is distributed under the same license as the MCDC package.
#
msgid ""
msgstr ""
"Project-Id-Version: MCDC\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-16 12:00+0700\n"
"PO-Revision-Date: 2024-08-16 12:00+0700\n"
"Last-Translator: \n"
"Language-Team: English\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

# Django REST Framework validation messages
msgid "This field is required."
msgstr "This field is required."

msgid "This field may not be blank."
msgstr "This field may not be blank."

msgid "This field may not be null."
msgstr "This field may not be null."

msgid "Enter a valid email address."
msgstr "Enter a valid email address."

msgid "Ensure this field has no more than {max_length} characters."
msgstr "Ensure this field has no more than {max_length} characters."

msgid "Ensure this field has at least {min_length} characters."
msgstr "Ensure this field has at least {min_length} characters."

msgid "Enter a valid value."
msgstr "Enter a valid value."

msgid "A valid integer is required."
msgstr "A valid integer is required."

msgid "A valid number is required."
msgstr "A valid number is required."

msgid "Enter a valid date."
msgstr "Enter a valid date."

msgid "Enter a valid time."
msgstr "Enter a valid time."

msgid "Enter a valid date/time."
msgstr "Enter a valid date/time."

msgid "Enter a valid duration."
msgstr "Enter a valid duration."

msgid "The submitted data was not a file. Check the encoding type on the form."
msgstr "The submitted data was not a file. Check the encoding type on the form."

msgid "No file was submitted."
msgstr "No file was submitted."

msgid "The submitted file is empty."
msgstr "The submitted file is empty."

msgid "Ensure this filename has at most {max_length} characters (it has {length})."
msgstr "Ensure this filename has at most {max_length} characters (it has {length})."

msgid "Please either submit a file or check the clear checkbox, not both."
msgstr "Please either submit a file or check the clear checkbox, not both."

msgid "Enter a list of values."
msgstr "Enter a list of values."

msgid "Enter a valid UUID."
msgstr "Enter a valid UUID."

msgid "Enter a valid URL."
msgstr "Enter a valid URL."

msgid "Enter a valid IPv4 address."
msgstr "Enter a valid IPv4 address."

msgid "Enter a valid IPv6 address."
msgstr "Enter a valid IPv6 address."

msgid "Enter a valid IPv4 or IPv6 address."
msgstr "Enter a valid IPv4 or IPv6 address."

msgid "Enter only digits separated by commas."
msgstr "Enter only digits separated by commas."

msgid "Ensure this value is {limit_value} (it is {show_value})."
msgstr "Ensure this value is {limit_value} (it is {show_value})."

msgid "Ensure this value is less than or equal to {limit_value}."
msgstr "Ensure this value is less than or equal to {limit_value}."

msgid "Ensure this value is greater than or equal to {limit_value}."
msgstr "Ensure this value is greater than or equal to {limit_value}."

msgid "Enter a valid choice."
msgstr "Enter a valid choice."

msgid "Enter a valid choice. That choice is not one of the available choices."
msgstr "Enter a valid choice. That choice is not one of the available choices."

msgid "Enter a whole number."
msgstr "Enter a whole number."

msgid "Ensure this value has at most {max_digits} digits in total."
msgstr "Ensure this value has at most {max_digits} digits in total."

msgid "Ensure this value has at most {max_decimal_places} decimal places."
msgstr "Ensure this value has at most {max_decimal_places} decimal places."

msgid "Ensure this value has at most {max_whole_digits} digits before the decimal point."
msgstr "Ensure this value has at most {max_whole_digits} digits before the decimal point."

# Custom field names for better error messages
msgid "username"
msgstr "username"

msgid "email"
msgstr "email"

msgid "password"
msgstr "password"

msgid "confirm_password"
msgstr "confirm password"

msgid "first_name"
msgstr "first name"

msgid "last_name"
msgstr "last name"

msgid "phone"
msgstr "phone"

msgid "otp_token"
msgstr "OTP token"

msgid "is_notification"
msgstr "notification"
